import { useTheme } from '@chakra-ui/react';
import { useEffect, useState, useRef } from 'react';
import GooglePlacesAutocomplete from 'react-google-places-autocomplete';
import { useFormContext } from 'react-hook-form';

import CampoPrototipo, {
  CampoPrototipoProps,
} from 'components/PDV/Geral/CampoPrototipo';
import {
  chakraComponents,
  ChakraReactSelectContainer,
} from 'components/PDV/Select/ReactSelectIntegracao';
import { SelectInputProps } from 'components/PDV/Select/SelectPadrao/Input';

const API_KEY = import.meta.env.VITE_API_GOOGLE_MAPS;

export type AutoCompleteProps = Omit<SelectInputProps, 'disabled' | 'options'> &
  Omit<CampoPrototipoProps, 'disabled' | 'options'> & {
    fontWeightLabel?: string;
    helperTextLabel?: string;
    color?: string;
    bgHelperText?: string;
    colorHelperText?: string;
  };

export function AutocompleteEndereco({
  name,
  label,
  color = 'black',
  bgHelperText = 'black',
  required = false,
  isDisabled,
  helperText,
  id,
  isMulti = false,
  colSpan,
  colStart,
  colEnd,
  rowSpan,
  rowStart,
  rowEnd,
  onClick,
  colorHelperText = 'white',
  errorPropName,
  errorText,
  helperTextLabel,
  shouldAppearTheAddress,
  renderOptionDetails,
  fontWeightLabel = 'semibold',
  labelColor,
  isLoading,
  ...selectProps
}: AutoCompleteProps) {
  const { colors, radii } = useTheme();
  const { setValue, watch } = useFormContext();
  const placeholderColor = colors.gray[400];

  // Estado local para controlar o valor exibido
  const [displayValue, setDisplayValue] = useState<any>(null);

  // Estado para forçar re-renderização do componente
  const [componentKey, setComponentKey] = useState(0);

  // Ref para rastrear o valor anterior do número
  const numeroAnteriorRef = useRef<string | undefined>(undefined);

  // Observa mudanças no campo número e endereço
  const numeroField = watch('endereco.value.numero');
  const enderecoCompleto = watch('endereco');

  // UseEffect separado para detectar mudanças no número e forçar re-renderização
  useEffect(() => {
    const numeroAtual = numeroField;
    const numeroAnterior = numeroAnteriorRef.current;

    // Se o número mudou (incluindo de/para vazio), força re-renderização
    if (numeroAtual !== numeroAnterior) {
      setComponentKey((prev) => prev + 1);
      numeroAnteriorRef.current = numeroAtual;
    }
  }, [numeroField]);

  // Atualiza o valor exibido quando o endereço ou número muda
  useEffect(() => {
    // Verifica se o número realmente mudou
    const numeroAtual = numeroField;

    if (enderecoCompleto) {
      if (enderecoCompleto.value && enderecoCompleto.value.endereco) {
        // Se tem endereço, sempre cria novo label (mesmo com número vazio)
        const numeroParaExibir = numeroAtual || '';
        const enderecoFormatado = `${
          enderecoCompleto.value.logradouro || enderecoCompleto.value.endereco
        }${numeroParaExibir ? `, ${numeroParaExibir}` : ''}${
          enderecoCompleto.value.bairro
            ? `, ${enderecoCompleto.value.bairro}`
            : ''
        }${
          enderecoCompleto.value.cidade
            ? ` - ${enderecoCompleto.value.cidade}`
            : ''
        }`;

        setDisplayValue({
          label: enderecoFormatado,
          value: {
            place_id: `custom_${Date.now()}_${numeroParaExibir}`, // ID único incluindo número
            description: enderecoFormatado,
          },
        });
      } else {
        setDisplayValue(enderecoCompleto);
      }
    } else {
      setDisplayValue(null);
    }

    // Atualiza o ref com o valor atual
    numeroAnteriorRef.current = numeroAtual;
  }, [enderecoCompleto, numeroField]);

  const obterDetalhesEndereco = async (placeId: string): Promise<any> => {
    return new Promise((resolve, reject) => {
      const service = new google.maps.places.PlacesService(
        document.createElement('div')
      );
      service.getDetails(
        {
          placeId,
          fields: ['address_components', 'formatted_address', 'geometry'],
        },
        (result, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK) {
            const obterDados = (type: string, usarSigla = false) =>
              result?.address_components?.find((c) => c.types.includes(type))?.[
                usarSigla ? 'short_name' : 'long_name'
              ] || '';

            resolve({
              endereco: result?.formatted_address || '',
              cep: obterDados('postal_code'),
              logradouro: obterDados('route'),
              numero: obterDados('street_number'),
              complemento: obterDados('subpremise') || '',
              bairro: obterDados('sublocality') || obterDados('neighborhood'),
              cidade:
                obterDados('locality') ||
                obterDados('administrative_area_level_2'),
              estado: obterDados('administrative_area_level_1', true),
              pais: obterDados('country'),
              latitude: result?.geometry?.location?.lat(),
              longitude: result?.geometry?.location?.lng(),
            });
          } else {
            reject('Erro ao obter informações do endereço');
          }
        }
      );
    });
  };

  return (
    <CampoPrototipo
      name={name}
      isRequired={required}
      helperTextLabel={helperTextLabel}
      isDisabled={isDisabled}
      helperText={helperText}
      colorHelperText={colorHelperText}
      color={color}
      id={id}
      label={label}
      labelColor={labelColor}
      bgHelperText={bgHelperText}
      rowSpan={rowSpan}
      rowStart={rowStart}
      rowEnd={rowEnd}
      colSpan={colSpan}
      colStart={colStart}
      colEnd={colEnd}
      errorPropName={errorPropName}
      errorText={errorText}
      fontWeightLabel={fontWeightLabel}
      actionLinkText={selectProps.actionLinkText}
      actionLinkOnClick={selectProps.actionLinkOnClick}
    >
      {({ formState: { isSubmitting } }, { value, ...fieldProps }) => {
        return (
          <ChakraReactSelectContainer>
            <GooglePlacesAutocomplete
              key={`autocomplete-${componentKey}-${
                displayValue?.value?.place_id || value?.label || 'empty'
              }`}
              selectProps={{
                isDisabled: isSubmitting || isDisabled,
                ...fieldProps,
                components: {
                  ...chakraComponents,
                },
                value: displayValue || value,
                onChange: async (val, props) => {
                  if (props?.action === 'clear') {
                    setValue(name, null);
                    setDisplayValue(null);
                    return;
                  }

                  const placeId = val?.value?.place_id;
                  if (!placeId) return;

                  // Se é um endereço customizado (criado por nós), não faz nada
                  if (placeId.startsWith('custom_')) return;

                  const dadosEndereco = await obterDetalhesEndereco(placeId);

                  const novoEndereco = {
                    label: dadosEndereco?.endereco,
                    value: dadosEndereco,
                  };

                  fieldProps.onChange(novoEndereco);

                  // Limpa o displayValue para usar o valor real do form
                  setDisplayValue(null);

                  setValue(
                    'endereco.value.numero',
                    dadosEndereco?.numero || ''
                  );
                },
                id: `select-container-${id}`,
                theme: (baseTheme) => ({
                  ...baseTheme,
                  borderRadius: radii.md,
                  colors: {
                    ...baseTheme.colors,
                    neutral50: placeholderColor,
                    neutral40: placeholderColor,
                  },
                  spacing: {
                    ...baseTheme.spacing,
                  },
                }),
                noOptionsMessage: () => null,
                placeholder: 'Digite o endereço da loja',
                styles: {
                  control: (provided) => ({
                    ...provided,
                    border: '1px solid #CCC',
                    boxShadow: 'none',
                    '&:hover': {
                      border: '1px solid #390073',
                      boxShadow: '0 0 0 1px #390073',
                    },
                    height: '40px !important',
                  }),
                  placeholder: (provided) => ({
                    ...provided,
                    color: '#aaa ',
                  }),
                  input: (provided) => ({
                    ...provided,
                    color: 'black',
                    lineHeight: 1,
                  }),
                  menuPortal: (base) => ({
                    ...base,
                    zIndex: 9999,
                  }),
                  menu: (provided) => ({
                    ...provided,
                    boxShadow: 'none',
                    zIndex: 1500,
                    marginTop: '1px',
                  }),
                  valueContainer: (provided) => ({
                    ...provided,
                    padding: '0.125rem 1rem',
                    fontSize: '1rem',
                  }),
                },
                isClearable: true,
                loadingMessage: () => 'Carregando...',
                menuPlacement: 'auto',
                className: 'react-select-container',
                classNamePrefix: 'react-select',
              }}
              apiKey={API_KEY}
              autocompletionRequest={{
                componentRestrictions: {
                  country: ['br'],
                },
              }}
              debounce={400}
              withSessionToken
            />
          </ChakraReactSelectContainer>
        );
      }}
    </CampoPrototipo>
  );
}
