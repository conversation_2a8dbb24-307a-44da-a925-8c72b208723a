import { Text, HStack, VStack, Flex } from '@chakra-ui/react';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { SimpleCard } from 'components/update/Form/SimpleCard';

import { VinculacaoProps } from '../../ModalEditarCaracteristicas/types';
import { TextoTooltip } from '../../TextoTooltip';

export const InformacoesProduto = ({
  dadosVinculacao,
  casasDecimaisValor,
  casasDecimaisQuantidade,
}: {
  dadosVinculacao: VinculacaoProps;
  casasDecimaisValor: number;
  casasDecimaisQuantidade: number;
}) => {
  return (
    <SimpleCard
      boxShadow="none"
      bg="gray.100"
      border="1px solid #4F01B1"
      borderRadius="5px"
      p={0}
      gap="0px"
      px="24px"
      py="14px"
    >
      <HStack w="full" justify="space-between">
        <HStack width="max-content">
          <VStack alignItems="flex-start" lineHeight="none">
            <Text color="gray.700" fontSize="12px">
              Produto:
            </Text>
            <Text color="violet.500" fontSize="14px" fontWeight="bold">
              {dadosVinculacao?.descricaoProduto}
            </Text>
            {dadosVinculacao?.dadosAdicionais && (
              <Flex flexDir="row" align="center" gap="4px" fontWeight="bold">
                <TextoTooltip
                  texto={dadosVinculacao?.dadosAdicionais}
                  maxWidth="100%"
                  fontSize="10px"
                  maximoLinhas={1}
                />
              </Flex>
            )}
          </VStack>
        </HStack>
        <HStack width="max-content" gap={{ base: '16px', lg: '68px' }}>
          <VStack alignItems="flex-start" lineHeight="none" whiteSpace="nowrap">
            <Text color="gray.700" fontSize="12px">
              Quantidade:
            </Text>
            <Text color="violet.500" fontSize="14px" fontWeight="bold">
              {DecimalMask(
                dadosVinculacao?.quantidade,
                casasDecimaisQuantidade,
                casasDecimaisQuantidade
              )}
            </Text>
          </VStack>
          <VStack alignItems="flex-start" lineHeight="none" whiteSpace="nowrap">
            <Text color="gray.700" fontSize="12px">
              Valor unitário:
            </Text>
            <Text color="violet.500" fontSize="14px" fontWeight="bold">
              R${'  '}
              {DecimalMask(
                dadosVinculacao?.valorUnitario,
                casasDecimaisValor,
                casasDecimaisValor
              )}
            </Text>
          </VStack>
          <VStack alignItems="flex-start" lineHeight="none" whiteSpace="nowrap">
            <Text color="gray.700" fontSize="12px">
              Valor total:
            </Text>
            <Text color="violet.500" fontSize="14px" fontWeight="bold">
              R$ {DecimalMask(dadosVinculacao?.valorTotal, 2, 2)}
            </Text>
          </VStack>
          <VStack alignItems="flex-start" lineHeight="none" whiteSpace="nowrap">
            <Text color="gray.700" fontSize="12px">
              Código de barras:
            </Text>
            <Text color="violet.500" fontSize="14px" fontWeight="bold">
              {dadosVinculacao?.codigoBarras || 'Não informado'}
            </Text>
          </VStack>
          <VStack alignItems="flex-start" lineHeight="none" whiteSpace="nowrap">
            <Text color="gray.700" fontSize="12px">
              CFOP:
            </Text>
            <Text color="violet.500" fontSize="14px" fontWeight="bold">
              {dadosVinculacao?.cfop || 'Não informado'}
            </Text>
          </VStack>
        </HStack>
      </HStack>
    </SimpleCard>
  );
};
