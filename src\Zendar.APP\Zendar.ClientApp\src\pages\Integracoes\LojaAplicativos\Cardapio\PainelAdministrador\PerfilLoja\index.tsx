import {
  <PERSON><PERSON>,
  <PERSON>lex,
  <PERSON><PERSON>tack,
  Text,
  GridItem,
  VStack,
  Button,
  Box,
  Input as ChakraIn<PERSON>,
  FormLabel,
  FormControl,
} from '@chakra-ui/react';
import { useCallback, useEffect, useState } from 'react';
import { FormProvider, useForm, Controller } from 'react-hook-form';
import { FiChevronLeft } from 'react-icons/fi';
import { toast } from 'react-toastify';

import {
  atualizarPerfilLojaDeliveryPainel,
  obterPerfilLojaDelivery,
  RedeSocial,
} from 'services/cardapio';

import { ContainerHeader } from 'pages/Integracoes/LojaAplicativos/Layout/ContainerHeader';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import Input from 'components/PDV/Input';
import InputTelefone from 'components/PDV/InputTelefonePdv';
import TextareaField from 'components/PDV/Textarea';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import { ContainerMidiasSociais } from '../../Etapas/MidiasSociais/MidiasSociais';
import { AutocompleteEndereco } from '../../Etapas/PerfilLoja/components';
import { usePainelCardapio } from '../hooks';

import { yupResolver } from './validationForms';

type Endereco = {
  label: string;
  value: {
    endereco: string;
    cep: string;
    logradouro: string;
    numero: string;
    complemento: string;
    bairro: string;
    cidade: string;
    estado: string;
    pais: string;
    latitude: number;
    longitude: number;
  };
};

type FormData = {
  nome: string;
  telefone: string;
  email: string;
  descricao: string;
  endereco: Endereco;
  [key: string]: string | Endereco;
};

export const PerfilLoja = () => {
  const { handleVoltarPainelAdm } = usePainelCardapio();
  const [isLoading, setIsLoading] = useState(false);

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
  });

  const { handleSubmit, setValue, watch } = formMethods;

  const handleSalvar = handleSubmit(async (data: FormData) => {
    setIsLoading(true);

    const { endereco, nome, telefone, email, descricao, ...rest } = data;

    const redesSociaisAtuais = [
      'whatsapp',
      'instagram',
      'facebook',
      'youtube',
      'x',
      'tiktok',
    ];

    const midiasSociais = redesSociaisAtuais
      .filter(
        (redeSocial) =>
          typeof rest[redeSocial] === 'string' &&
          (rest[redeSocial] as string).trim() !== ''
      )
      .map((rede) => ({
        tipo: rede.toUpperCase() as RedeSocial,
        valor: rest[rede] as string,
      }));

    // const response = await atualizarPerfilLojaDeliveryPainel({
    //   descricao: descricao || '',
    //   email: email || '',
    //   nome: nome || '',
    //   telefone: telefone || '',
    //   endereco: {
    //     endereco: endereco?.value?.endereco || '',
    //     cep: endereco?.value?.cep || '',
    //     logradouro: endereco?.value?.logradouro || '',
    //     numero: endereco?.value?.numero || '',
    //     complemento: endereco?.value?.complemento || '',
    //     bairro: endereco?.value?.bairro || '',
    //     cidade: endereco?.value?.cidade || '',
    //     estado: endereco?.value?.estado || '',
    //     pais: endereco?.value?.pais || '',
    //     latitude: endereco?.value?.latitude || 0,
    //     longitude: endereco?.value?.longitude || 0,
    //   },
    //   midiaSocial: {
    //     midiasSociais,
    //   },
    // });

    // if (response) {
    //   toast.success('As alterações foram salvas com sucesso!');
    //   setIsLoading(false);
    // }
    // setIsLoading(false);
  });

  const obterInformacaoSalva = useCallback(async () => {
    setIsLoading(true);
    const dados = await obterPerfilLojaDelivery();
    if (dados) {
      const endereco = {
        label: dados?.endereco?.endereco,
        value: {
          endereco: dados?.endereco?.endereco,
          cep: dados.endereco?.cep,
          logradouro: dados.endereco?.logradouro,
          numero: dados.endereco?.numero,
          complemento: dados.endereco?.complemento,
          bairro: dados.endereco?.bairro,
          cidade: dados.endereco?.cidade,
          estado: dados.endereco?.estado,
          pais: dados.endereco?.pais,
          latitude: dados.endereco?.latitude,
          longitude: dados.endereco?.longitude,
        },
      };
      setValue('nome', dados?.nome);
      setValue('telefone', dados?.telefone);
      setValue('email', dados?.email);
      setValue('endereco', endereco);
      setValue('descricao', dados?.descricao);

      const midiasSalvas = dados?.midiaSocial?.midiasSociais;

      if (Array.isArray(midiasSalvas)) {
        midiasSalvas.forEach(({ tipo, valor }) => {
          const redeSocialId = tipo?.toLowerCase();

          if (redeSocialId) {
            setValue(redeSocialId as any, valor);
          }
        });
      }
    }
    setIsLoading(false);
  }, [setValue]);

  useEffect(() => {
    obterInformacaoSalva();
  }, [obterInformacaoSalva]);

  return (
    <FormProvider {...formMethods}>
      {isLoading && <LoadingPadrao />}
      <ContainerHeader
        bg="primary.500"
        gap={2}
        color="white"
        alignItems="center"
      >
        <Box cursor="pointer" mr="10px" onClick={handleVoltarPainelAdm}>
          <Icon fontSize="25px" as={FiChevronLeft} />
        </Box>
        <Text fontSize="16px" fontWeight="semibold">
          Perfil da loja
        </Text>
      </ContainerHeader>
      <VStack
        mb="24px"
        color="primary.50"
        fontSize="14px"
        spacing="24px"
        px="24px"
        py="28px"
      >
        <SimpleGridForm w="full" gap="24px">
          <GridItem colSpan={[12, 12, 6, 4]}>
            <Input
              id="nome"
              name="nome"
              placeholder="Ex: Recanto Lanches e Porções"
              label="Nome da loja para exibição no cardápio"
              border="1px solid"
              borderColor="gray.200"
              _placeholder={{ color: '#BBBBBB' }}
              height="40px"
            />
          </GridItem>
          <GridItem colSpan={[12, 12, 6, 2]}>
            <InputTelefone
              id="telefone"
              name="telefone"
              placeholder="(00) 00000-0000"
              label="Telefone"
              border="1px solid"
              borderColor="gray.200"
              _placeholder={{ color: '#BBBBBB' }}
              height="40px"
            />
          </GridItem>
          <GridItem colSpan={[12, 12, 12, 6]}>
            <Input
              id="email"
              name="email"
              label="E-mail"
              placeholder="Digite o endereço de e-mail"
              colSpan={6}
              maxLength={60}
              border="1px solid"
              borderColor="gray.200"
              _placeholder={{ color: '#BBBBBB' }}
              height="40px"
            />
          </GridItem>
          <GridItem colSpan={[12, 12, 12, 6]}>
            <SimpleGridForm w="full" gap="24px">
              <GridItem colSpan={[12, 12, 12, 10]}>
                <AutocompleteEndereco
                  name="endereco"
                  id="endereco"
                  label="Endereço"
                  placeholder="Digite o endereço para retirada dos pedidos"
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 12, 2]}>
                <Controller
                  name="endereco.value.numero"
                  render={({ field }) => (
                    <Input
                      id="endereco.value.numero"
                      name="endereco.value.numero"
                      label="Número"
                      placeholder="0000"
                      colSpan={6}
                      maxLength={60}
                      border="1px solid"
                      borderColor="gray.200"
                      _placeholder={{ color: '#BBBBBB' }}
                      height="40px"
                      value={field.value || ''}
                      onChange={(e) => {
                        const novoNumero = e.target.value;
                        field.onChange(novoNumero);

                        // Atualiza o valor no objeto endereco
                        const enderecoAtual = watch('endereco');
                        if (enderecoAtual?.value) {
                          setValue('endereco', {
                            ...enderecoAtual,
                            value: {
                              ...enderecoAtual.value,
                              numero: novoNumero,
                            },
                          });
                        }
                      }}
                    />
                  )}
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 12, 12]}>
                <Flex flexDir="column" gap="28px">
                  <TextareaField
                    name="descricao"
                    id="descricao"
                    placeholder="Descreva o seu estabelecimento e suas principais características."
                    label="Texto de descrição"
                    border="1px solid"
                    color="gray.700"
                    borderColor="gray.100"
                    minH="266px"
                    maxH="560px"
                    _placeholder={{ color: 'gray.200' }}
                  />
                </Flex>
              </GridItem>
            </SimpleGridForm>
          </GridItem>

          <GridItem colSpan={[12, 12, 12, 6]}>
            <Text fontSize="14px" color="black">
              Contatos e Redes Sociais
            </Text>
            <Flex
              w="full"
              bg="gray.100"
              borderWidth="1px"
              borderColor="gray.200"
              borderRadius="5px"
              py="24px"
              px={['12px', '12px', '40px', '40px', '40px']}
            >
              <ContainerMidiasSociais />
            </Flex>
          </GridItem>
        </SimpleGridForm>
      </VStack>
      <Flex
        w="full"
        justify="center"
        gap={['12px', '24px']}
        pb="60px"
        flexDir={['column', 'row']}
        px="24px"
      >
        <Button
          w={['full', '96px']}
          variant="outlineDefault"
          colorScheme="gray"
          borderRadius="full"
          fontWeight="normal"
          fontSize="14px"
          height="32px"
          onClick={handleVoltarPainelAdm}
        >
          Cancelar
        </Button>
        <Button
          w={['full', '200px']}
          colorScheme="aquamarine"
          borderRadius="full"
          onClick={handleSalvar}
          fontSize="14px"
          fontWeight="normal"
          height="32px"
        >
          Salvar
        </Button>
      </Flex>
    </FormProvider>
  );
};
