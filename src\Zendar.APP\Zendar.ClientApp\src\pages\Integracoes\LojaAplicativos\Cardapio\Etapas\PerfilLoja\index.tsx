import { Text, VStack, Box, useMediaQuery, GridItem } from '@chakra-ui/react';
import { useCallback, useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';

import { obterPerfilLojaDelivery } from 'services/cardapio';

import {
  ContainerIntegracaoFixo,
  useEtapasContext,
} from 'store/Cardapio/Etapas/EtapasContext';
import { usePerfilLoja } from 'store/Cardapio/perfilLoja.store';

import Input from 'components/PDV/Input';
import InputTelefone from 'components/PDV/InputTelefonePdv';
import { SimpleGridForm } from 'components/update/Form/SimpleGridForm';

import { IdentificacaoEtapasCardapio } from 'constants/enum/IdentificacaoEtapasCardapio';
import { ConfiguracoesMinhasLojasIcon } from 'icons';

import { Footer } from '../Layout/Footer';
import { Header } from '../Layout/Header';

import { AutocompleteEndereco } from './components';
import { yupResolver } from './validationForms';

type FormData = {
  nome: string;
  telefone: string;
  email: string;
  endereco: {
    label: string;
    value: {
      endereco: string;
      cep: string;
      logradouro: string;
      numero: string;
      complemento: string;
      bairro: string;
      cidade: string;
      estado: string;
      pais: string;
      latitude: number;
      longitude: number;
    };
  };
};

export const PerfilLoja = () => {
  const { setPassoAtual, setIsLoading } = useEtapasContext();
  const { setDados, dados, obteveDadosAPI, setObteveDadosAPI } =
    usePerfilLoja();
  const [isLargerThan700] = useMediaQuery('(min-width: 700px)');

  const formMethods = useForm<FormData>({
    resolver: yupResolver,
    defaultValues: { ...dados, endereco: dados.endereco ?? null } as FormData,
  });

  const { handleSubmit, setValue } = formMethods;

  const handleAvancar = handleSubmit(async (data: FormData) => {
    setIsLoading(true);
    const { endereco } = data;
    setDados({
      nome: data.nome,
      telefone: data.telefone,
      email: data.email,
      endereco: {
        label: endereco?.label,
        value: {
          endereco: endereco.value?.endereco,
          cep: endereco.value?.cep,
          logradouro: endereco.value?.logradouro,
          numero: endereco.value?.numero,
          complemento: endereco.value?.complemento,
          bairro: endereco.value?.bairro,
          cidade: endereco.value?.cidade,
          estado: endereco.value?.estado,
          pais: endereco.value?.pais,
          latitude: endereco.value?.latitude,
          longitude: endereco.value?.longitude,
        },
      },
    });
    setPassoAtual(IdentificacaoEtapasCardapio.DESCRICAO_LOJA);
    setIsLoading(false);
  });

  const handleVoltar = () => {
    setPassoAtual(IdentificacaoEtapasCardapio.IDENTIFICADOR_URL);
  };

  const obterInformacaoSalva = useCallback(async () => {
    setIsLoading(true);

    const dados = await obterPerfilLojaDelivery();
    if (dados) {
      const endereco = {
        label: dados?.endereco?.endereco,
        value: {
          endereco: dados?.endereco?.endereco,
          cep: dados.endereco?.cep,
          logradouro: dados.endereco?.logradouro,
          numero: dados.endereco?.numero,
          complemento: dados.endereco?.complemento,
          bairro: dados.endereco?.bairro,
          cidade: dados.endereco?.cidade,
          estado: dados.endereco?.estado,
          pais: dados.endereco?.pais,
          latitude: dados.endereco?.latitude,
          longitude: dados.endereco?.longitude,
        },
      };
      setValue('nome', dados?.nome);
      setValue('telefone', dados?.telefone);
      setValue('email', dados?.email);
      setValue('endereco', endereco);

      const novosDadosSalvos = {
        ...dados,
        endereco,
      };
      setDados(novosDadosSalvos);
      setObteveDadosAPI();
    }
    setIsLoading(false);
  }, [setValue, setIsLoading]);

  useEffect(() => {
    if (obteveDadosAPI) return;
    obterInformacaoSalva();
  }, [obterInformacaoSalva, obteveDadosAPI]);

  return (
    <FormProvider {...formMethods}>
      <ContainerIntegracaoFixo height={isLargerThan700 ? '100vh' : '100%'}>
        <Header
          title="Perfil da Loja"
          handleVoltar={handleVoltar}
          icon={ConfiguracoesMinhasLojasIcon}
        />
        <Box
          pl={['10px', '10px', '20px']}
          pr={['10px', '10px', '20px']}
          maxW="600px"
        >
          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <Text
              textAlign={isLargerThan700 ? undefined : 'left'}
              letterSpacing="0px"
            >
              Informe o nome que será exibido no topo do aplicativo e a
              descrição que ficará disponível no perfil da loja para que os
              clientes conheçam seus diferenciais e pratos principais:
            </Text>
          </VStack>

          <VStack mb="24px" color="primary.50" fontSize="14px" spacing="24px">
            <SimpleGridForm w="full">
              <GridItem colSpan={12}>
                <Input
                  id="nome"
                  name="nome"
                  placeholder="Ex: Recanto Lanches e Porções"
                  label="Nome da loja para exibição no cardápio"
                  border="1px solid"
                  borderColor="gray.200"
                  _placeholder={{ color: '#BBBBBB' }}
                  height="40px"
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 6, 6, 6]}>
                <InputTelefone
                  id="telefone"
                  name="telefone"
                  placeholder="(00) 00000-0000"
                  label="Telefone"
                  border="1px solid"
                  borderColor="gray.200"
                  _placeholder={{ color: '#BBBBBB' }}
                  height="40px"
                />
              </GridItem>
              <GridItem colSpan={[12, 12, 6, 6, 6]}>
                <Input
                  id="email"
                  name="email"
                  label="E-mail"
                  placeholder="Digite o endereço de e-mail"
                  colSpan={6}
                  maxLength={60}
                  border="1px solid"
                  borderColor="gray.200"
                  _placeholder={{ color: '#BBBBBB' }}
                  height="40px"
                />
              </GridItem>
              <GridItem colSpan={12}>
                <AutocompleteEndereco
                  name="endereco"
                  id="endereco"
                  label="Endereço"
                />
              </GridItem>
            </SimpleGridForm>
          </VStack>
        </Box>
      </ContainerIntegracaoFixo>
      <Footer
        mt={['20px', '0', '0']}
        mb={['20px', '0', '0']}
        handleAvancar={handleAvancar}
      />
    </FormProvider>
  );
};
