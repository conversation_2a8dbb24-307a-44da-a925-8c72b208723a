import {
  Tr,
  Td,
  Flex,
  Icon,
  Box,
  Divider,
  HStack,
  Text,
  Button,
  Table,
  Thead,
  Tbody,
  Th,
} from '@chakra-ui/react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FiChevronUp } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { InfoTooltip } from 'components/update/Tooltip/InfoTooltip';

import { SalvarInserirNovoIcon } from 'icons';

import { Produto, InformacoesRodape } from '../types';

type ListagemLancamentoManualProps = {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  buscarMaisItensNoScroll: () => Promise<void>;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditarProduto: (index: number) => void;
  handleRemoverProduto: (index: number) => void;
  foiLancadoEstoque: boolean;
  isReadOnly: boolean;
  casasDecimais: {
    casasDecimaisQuantidade: number;
    casasDecimaisValor: number;
  };
  isLoading?: boolean;
  modoTelaCheia?: boolean;
  isOpen: (produtoId: string) => boolean;
  handleAdicionarProduto: () => void;
  naoPodeLancarItens: boolean;
};

function useColWidths() {
  const [larguras, setLarguras] = useState<number[]>([]);

  useEffect(() => {
    function calcular() {
      const total = window.innerWidth - 210;
      const porcentagens = [60, 10, 8, 10, 12];
      setLarguras(porcentagens.map((p) => (total * p) / 100));
    }

    calcular();
    window.addEventListener('resize', calcular);
    return () => window.removeEventListener('resize', calcular);
  }, []);

  return larguras;
}

export const ListagemLancamentoManual = ({
  produtos,
  informacoesRodape,
  buscarMaisItensNoScroll,
  handleToggleLinhaProduto,
  handleEditarProduto,
  handleRemoverProduto,
  foiLancadoEstoque,
  isReadOnly,
  casasDecimais,
  isLoading = false,
  modoTelaCheia = false,
  isOpen,
  handleAdicionarProduto,
  naoPodeLancarItens,
}: ListagemLancamentoManualProps) => {
  const [scrollEl, setScrollEl] = useState<HTMLDivElement | null>(null);
  const larguras = useColWidths();
  const rowVirtualizer = useVirtualizer({
    count: produtos?.length ?? 0,
    getScrollElement: () => scrollEl,
    estimateSize: (index) =>
      isOpen(produtos[index]?.entradaMercadoriaItemId ?? '') ? 115 : 64,
    overscan: 5,
    getItemKey: (index) => produtos[index]?.entradaMercadoriaItemId ?? index,
    measureElement: (el) => el.getBoundingClientRect().height,
  });

  const measureRows = useCallback(() => {
    rowVirtualizer.measure();
  }, [rowVirtualizer]);

  const colunas = useMemo<ColumnDef<Produto>[]>(
    () => [
      {
        id: 'nomeProduto',
        header: () => {
          return naoPodeLancarItens ? (
            'Produto'
          ) : (
            <Button
              size="xs"
              borderRadius="full"
              colorScheme="secondary"
              leftIcon={<Icon as={SalvarInserirNovoIcon} fontSize="sm" />}
              minW="164px"
              height="32px"
              fontSize="sm"
              fontStyle="italic"
              onClick={handleAdicionarProduto}
              cursor="pointer"
              style={{ marginLeft: '2px' }}
            >
              Adicionar itens
            </Button>
          );
        },
        size: larguras[0],
        meta: {
          paddingLeft: naoPodeLancarItens ? '56px' : '0px !important',
        },
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;

          return (
            <Box
              cursor="pointer"
              userSelect="none"
              fontSize="14px"
              onClick={() => {
                measureRows();
                handleToggleLinhaProduto(index);
              }}
            >
              <Flex alignItems="center" gap="8px">
                <Button
                  tabIndex={0}
                  bg="transparent"
                  p="4px"
                  pb="0px"
                  mr="6px"
                  h="fit-content"
                  borderRadius="6px"
                  _focus={{
                    background: 'gray.100',
                  }}
                  minW="16px"
                >
                  <Icon
                    as={FiChevronUp}
                    mb="6px"
                    transform={produto.isOpen ? '' : 'rotate(180deg)'}
                    role="button"
                    transition="all 0.3s"
                  />
                </Button>

                <Text overflowWrap="anywhere" noOfLines={2}>
                  {produto.nomeProduto}
                </Text>
              </Flex>
            </Box>
          );
        },
      },
      {
        id: 'quantidade',
        header: 'Quantidade',
        size: larguras[1],
        cell: ({ row }) => {
          const produto = row.original;

          return (
            <Text fontSize="14px">
              {DecimalMask(
                produto.quantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Text>
          );
        },
      },
      {
        id: 'valorUnitarioEntrada',
        header: 'Valor Unitário',
        size: larguras[2],
        cell: ({ row }) => {
          const produto = row.original;

          return (
            <Text fontSize="14px" textAlign="right">
              {DecimalMask(
                produto.valorUnitarioEntrada,
                casasDecimais.casasDecimaisValor
              )}
            </Text>
          );
        },
        meta: {
          textAlign: 'right',
        },
      },
      {
        id: 'valorTotal',
        header: 'Valor Total',
        cell: ({ row }) => {
          const produto = row.original;

          return (
            <Text fontSize="14px" textAlign="right">
              {DecimalMask(produto.valorTotal, 2, 2)}
            </Text>
          );
        },
        size: larguras[3],
        meta: {
          textAlign: 'right',
        },
      },
      {
        id: 'acoes',
        header: 'Ações',
        size: larguras[4],
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;

          return (
            <Flex justify="end">
              <ActionsMenu
                isDisabled={
                  foiLancadoEstoque || isReadOnly || produto.bloquearAlteracao
                }
                items={[
                  {
                    content: 'Editar',
                    onClick: () => {
                      handleEditarProduto(index);
                    },
                  },
                  {
                    content: 'Remover',
                    onClick: () => {
                      handleRemoverProduto(index);
                    },
                  },
                ]}
              />
            </Flex>
          );
        },
        meta: {
          textAlign: 'right',
          paddingRight: '28px !important',
        },
      },
    ],
    [
      larguras,
      naoPodeLancarItens,
      handleAdicionarProduto,
      measureRows,
      handleToggleLinhaProduto,
      casasDecimais.casasDecimaisQuantidade,
      casasDecimais.casasDecimaisValor,
      foiLancadoEstoque,
      isReadOnly,
      handleEditarProduto,
      handleRemoverProduto,
    ]
  );

  const table = useReactTable({
    data: produtos,
    columns: colunas,
    getCoreRowModel: getCoreRowModel(),
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  useEffect(() => {
    rowVirtualizer.measure();
  }, [modoTelaCheia, produtos.length, rowVirtualizer]);

  useEffect(() => {
    const [lastItem] = [...virtualItems].reverse();

    if (!lastItem) {
      return;
    }

    if (
      lastItem.index >= produtos.length - 1 &&
      produtos.length < informacoesRodape.totalProdutos &&
      !isLoading
    ) {
      buscarMaisItensNoScroll();
    }
  }, [
    virtualItems,
    produtos.length,
    informacoesRodape.totalProdutos,
    isLoading,
    buscarMaisItensNoScroll,
  ]);

  return (
    <>
      <Box
        ref={setScrollEl}
        overflow="auto"
        transition="height 0.3s"
        position="relative"
        height={informacoesRodape.totalProdutos === 0 ? '140px' : 'auto'}
      >
        <Table variant="simple" size="sm">
          <Thead bg="white" position="sticky" top={0} zIndex={1}>
            {table.getHeaderGroups().map((headerGroup) => (
              <Tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <Th
                      key={header.id}
                      {...header.column.columnDef.meta}
                      width={`${header.getSize()}px`}
                      minWidth={`${header.getSize()}px`}
                      maxWidth={`${header.getSize()}px`}
                      borderBottom="1px solid"
                      borderColor="gray.200"
                      lineHeight="none"
                      verticalAlign="bottom"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </Th>
                  );
                })}
              </Tr>
            ))}
          </Thead>
        </Table>
        <Box
          height={`${rowVirtualizer.getTotalSize()}px`}
          width="100%"
          position="relative"
        >
          {virtualItems.map((virtualItem) => {
            const produto = produtos[virtualItem.index];
            const isExpanded = isOpen(produto?.entradaMercadoriaItemId ?? '');
            const row = table.getRowModel().rows[virtualItem.index];
            if (!row) return null;

            return (
              <Box
                key={virtualItem.key}
                position="absolute"
                top={0}
                left={0}
                width="100%"
                height={`${virtualItem.size}px`}
                transform={`translateY(${virtualItem.start}px)`}
              >
                <Table
                  style={{
                    tableLayout: 'fixed',
                    width: '100%',
                    borderCollapse: 'separate',
                    borderSpacing: 0,
                    borderTopLeftRadius: '5px',
                    borderTopRightRadius: '5px',
                    borderBottomLeftRadius: isExpanded ? '0px' : '5px',
                    borderBottomRightRadius: isExpanded ? '0px' : '5px',
                    overflow: 'hidden',
                    border: '1px solid #e8e8e8',
                  }}
                >
                  <Tbody>
                    <Tr
                      transition="all 0.3s"
                      sx={
                        isExpanded
                          ? {
                              '& > td': {
                                marginBottom: '5px',
                                borderBottomRadius: '0px !important',
                              },
                            }
                          : {}
                      }
                    >
                      {row.getVisibleCells().map((cell) => (
                        <Td
                          key={cell.id}
                          {...cell.column.columnDef.meta}
                          width={`${cell.column.getSize()}px`}
                          minWidth={`${cell.column.getSize()}px`}
                          maxWidth={`${cell.column.getSize()}px`}
                          overflow="hidden"
                          textOverflow={
                            cell.column.id === 'nomeProduto'
                              ? 'unset'
                              : 'ellipsis'
                          }
                          whiteSpace={
                            cell.column.id === 'nomeProduto'
                              ? 'normal'
                              : 'nowrap'
                          }
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </Td>
                      ))}
                    </Tr>
                  </Tbody>
                </Table>

                {isExpanded && (
                  <Box
                    h="52px"
                    bg="white"
                    px="5"
                    mt="-3px"
                    transition="all 0.3s"
                    border="1px"
                    borderColor="gray.100"
                    borderTop="none"
                    borderBottomLeftRadius="5px"
                    borderBottomRightRadius="5px"
                  >
                    <Divider />

                    <HStack
                      spacing="6"
                      px="5"
                      pl="26px"
                      h="full"
                      lineHeight="none"
                      fontSize="xs"
                    >
                      <Flex>
                        <Text fontWeight="light">ICMS ST:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.valorIcmsSt, 2, 2)}
                        </Text>
                      </Flex>
                      <Divider orientation="vertical" h="6" />
                      <Flex>
                        <Text fontWeight="light">IPI:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.valorIpi, 2, 2)}
                        </Text>
                      </Flex>
                      <Divider orientation="vertical" h="6" />
                      <Flex>
                        <Text fontWeight="light">FCP ST:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.valorFcpSt, 2, 2)}
                        </Text>
                      </Flex>
                      <Divider orientation="vertical" h="6" />
                      <Flex h="center" alignItems="center">
                        <Text fontWeight="light">Custo adicional:</Text>
                        <Text ml="2" fontWeight="bold">
                          <Text as="span" fontSize="2xs" mr="0.5">
                            R$
                          </Text>
                          {DecimalMask(produto.custoAdicional, 2, 2)}
                        </Text>

                        <Box ml="2">
                          <InfoTooltip
                            label="Os valores deste campo não serão somados ao valor total da entrada, servindo apenas para compor o custo do produto."
                            tabIndex={-1}
                          />
                        </Box>
                      </Flex>
                    </HStack>
                  </Box>
                )}
              </Box>
            );
          })}
        </Box>

        {produtos.length === 0 && !isLoading && (
          <Box p="0" textAlign="center" borderRadius="md">
            <Table style={{ tableLayout: 'fixed', width: '100%' }}>
              <Tbody>
                <Tr transition="all 0.3s" h="58px">
                  <Td>Nenhum produto informado</Td>
                </Tr>
              </Tbody>
            </Table>
          </Box>
        )}
      </Box>
    </>
  );
};
