import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query';
import { useCallback, useMemo, useRef } from 'react';

import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';

import api, { ResponseApi } from 'services/api';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

import { Produto, ProdutoPaginadoRetorno, InformacoesRodape } from '../types';

type BuscarItensPaginacaoParams = {
  pageParam: number;
  id: string;
};

const buscarItensPaginacao = async ({
  pageParam,
  id,
}: BuscarItensPaginacaoParams): Promise<ProdutoPaginadoRetorno> => {
  const paginationData = {
    currentPage: pageParam,
    pageSize: 25,
    orderColumn: 'nomeProduto',
    orderDirection: 'asc',
  };

  const response = await api.get<void, ResponseApi<ProdutoPaginadoRetorno>>(
    formatQueryPagegTable(
      ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_LISTAR_ITENS_PAGINADOS,
      paginationData
    ),
    { params: { id } }
  );

  if (response?.sucesso && response?.dados) {
    return {
      ...response.dados,
      registros: response.dados.registros.map((produto) => ({
        ...produto,
        isOpen: false,
      })),
    };
  }

  return {
    totalProdutos: 0,
    totalItens: 0,
    valorTotal: 0,
    registros: [],
    bloquearAlteracao: false,
  };
};

export const useProdutosLancamento = (entradaMercadoriaId: string) => {
  const queryClient = useQueryClient();
  const lockRef = useRef(false);

  const {
    data,
    fetchNextPage,
    isLoading: isLoadingQuery,
    isFetchingNextPage,
    isFetching,
    hasNextPage,
    refetch,
  } = useInfiniteQuery<ProdutoPaginadoRetorno>({
    queryKey: ['produtos-lancamento', entradaMercadoriaId],
    queryFn: ({ pageParam }) =>
      buscarItensPaginacao({
        pageParam: (pageParam as number) || 1,
        id: entradaMercadoriaId,
      }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const totalPaginas = Math.ceil((lastPage.totalProdutos ?? 0) / 25);
      const paginaAtual = allPages.length;
      return paginaAtual < totalPaginas ? paginaAtual + 1 : undefined;
    },
    enabled: !!entradaMercadoriaId,
    refetchOnWindowFocus: false,
  });

  const produtos = useMemo(
    () => data?.pages?.flatMap((page) => page.registros) ?? [],
    [data]
  );

  const informacoesRodape = useMemo((): InformacoesRodape => {
    const primeiraPage = data?.pages?.[0];
    return {
      totalProdutos: primeiraPage?.totalProdutos ?? 0,
      quantidadeItens: primeiraPage?.totalItens ?? 0,
      valorTotalProdutos: primeiraPage?.valorTotal ?? 0,
    };
  }, [data]);

  const buscarMaisItensNoScroll = useCallback(async (): Promise<void> => {
    if (hasNextPage && !isFetchingNextPage && !lockRef.current) {
      lockRef.current = true;
      try {
        await fetchNextPage();
      } catch (error) {
        console.error('Erro ao buscar mais itens:', error);
      } finally {
        setTimeout(() => {
          lockRef.current = false;
        }, 150);
      }
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleToggleLinhaProduto = useCallback(
    (index: number) => {
      queryClient.setQueryData(
        ['produtos-lancamento', entradaMercadoriaId],
        (oldData: any) => {
          if (!oldData) return oldData;

          const newPages = oldData.pages.map(
            (page: ProdutoPaginadoRetorno) => ({
              ...page,
              registros: page.registros.map(
                (produto: Produto, produtoIndex: number) => {
                  // Calcular o índice global do produto
                  const globalIndex =
                    oldData.pages
                      .slice(0, oldData.pages.indexOf(page))
                      .reduce(
                        (acc: number, p: ProdutoPaginadoRetorno) =>
                          acc + p.registros.length,
                        0
                      ) + produtoIndex;

                  if (globalIndex === index) {
                    return {
                      ...produto,
                      isOpen: !produto.isOpen,
                    };
                  }
                  return produto;
                }
              ),
            })
          );

          return {
            ...oldData,
            pages: newPages,
          };
        }
      );
    },
    [queryClient, entradaMercadoriaId]
  );

  const isOpen = useCallback(
    (produtoId: string) => {
      const produto = produtos.find(
        (p) => p.entradaMercadoriaItemId === produtoId
      );
      return produto?.isOpen ?? false;
    },
    [produtos]
  );

  const recarregarLista = useCallback(async () => {
    await refetch();
  }, [refetch]);

  const isLoading = isLoadingQuery || isFetching;

  return {
    produtos,
    informacoesRodape,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    buscarMaisItensNoScroll,
    handleToggleLinhaProduto,
    isOpen,
    recarregarLista,
  };
};
